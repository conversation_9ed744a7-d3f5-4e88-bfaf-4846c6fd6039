name: MakeImageAndPush
on:
  push:
    tags:
      - v*

jobs:
  release-image:
    runs-on: ubuntu-latest
    container:
      image: catthehacker/ubuntu:act-latest
    env:
      DOCKER_ORG: git.978543210.com/iot-rd
      IMAGE_NAME: ts-iot-sys-webui
      DOCKER_LATEST: latest
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ gitea.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by Gite<PERSON>!"
      - run: echo "🔎 The name of your branch is ${{ gitea.ref }} and your repository is ${{ gitea.repository }}."

      - name: Checkout
        uses: https://gitea.com/actions/checkout@v4
        with:
          fetch-depth: 0 # all history for all branches and tags

      - run: echo "💡 The ${{ gitea.repository }} repository has been cloned to the runner."
      - run: echo "💡 The ${{ gitea.repository_owner }} own the repository. And the CI_TOKEN is ${{ secrets.CI_W_TOKEN}}, CI_USER is ${{ secrets.CI_USER}}"

      - run: echo "🖥️ The workflow is now ready to test your code on the runner."

      - name: List files in the repository
        run: |
          ls ${{ gitea.workspace }}          

      - name: Set up QEMU
        uses: https://gitea.com/docker/setup-qemu-action@v3

      - name: Set up Docker BuildX
        uses: https://gitea.com/docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: https://gitea.com/docker/login-action@v3
        with:
          registry: git.978543210.com
          username: ${{ secrets.CI_USER }}
          password: ${{ secrets.CI_W_TOKEN }}

      - name: Get Meta
        id: meta
        run: |
          echo REPO_NAME=$(echo ${GITHUB_REPOSITORY} | awk -F"/" '{print $2}') >> $GITHUB_OUTPUT
          echo REPO_VERSION=$(git describe --tags --always | sed 's/^v//') >> $GITHUB_OUTPUT
          VERSION=$(echo "${{ gitea.ref }}" | sed -e 's,.*/\(.*\),\1,')
          [[ "${{ gitea.ref }}" == "refs/tags/"* ]] && VERSION=$(echo $VERSION | sed -e 's/^v//')
          [ "$VERSION" == "main" ] && VERSION=latest
          echo VERSION=$VERSION >> $GITHUB_OUTPUT          

      - name: Build and push
        uses: https://gitea.com/docker/build-push-action@v5
        with:
          context: .
          file: ./Deploy/Dockerfile
          platforms: |
            linux/amd64
          push: true
          tags: |
            ${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.VERSION }}            
