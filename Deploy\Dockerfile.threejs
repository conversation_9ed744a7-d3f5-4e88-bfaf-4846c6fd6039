# node 构建
FROM git.978543210.com/iot-rd/node:22.12.0 AS build-stage
WORKDIR /app
RUN rm -rf node_modules
COPY . ./
# 设置 node 阿里镜像
RUN npm config set registry https://registry.npmmirror.com
# 设置--max-old-space-size
ENV NODE_OPTIONS=--max-old-space-size=16384
# 前端应用不需要后端 API 代理，设置为相对路径
ENV VITE_APP_BASE_URL=/
# 设置阿里镜像、pnpm、依赖、编译
RUN npm cache clean --force && \
    npm install -g cnpm && \
    yarn --version && \
    rm -rf node_modules && \
    rm -rf package-lock.json && \
    npm install --force && \
    npm install ace-builds && \
    npm install -D sass && \
    npm run build
# node部分结束
RUN echo "🎉 threejs 前端编译成功 🎉"

# nginx 部署
FROM git.978543210.com/iot-rd/nginx:1.25.4 AS production-stage
COPY --from=build-stage /app/threejs-3dmodel-edit /usr/share/nginx/html
COPY --from=build-stage /app/Deploy/nginx.threejs.conf /etc/nginx/nginx.conf
EXPOSE 80
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    echo "🎉 threejs 前端部署成功 🎉"
CMD nginx -g 'daemon off;'
