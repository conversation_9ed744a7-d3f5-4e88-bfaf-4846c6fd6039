# node 构建
FROM git.978543210.com/iot-rd/node:22.12.0 AS build-stage
WORKDIR /app

# 设置环境变量
ENV NODE_OPTIONS=--max-old-space-size=16384
ENV VITE_APP_BASE_URL=/

# 设置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制 package 文件并安装依赖（利用 Docker 缓存）
COPY package*.json ./
RUN rm -f package-lock.json && \
    npm install --silent

# 复制源代码并构建
COPY . ./
RUN npm run build
# node部分结束
RUN echo "🎉 threejs 前端编译成功 🎉"

# nginx 部署
FROM git.978543210.com/iot-rd/nginx:1.25.4 AS production-stage
COPY --from=build-stage /app/threejs-3dmodel-edit /usr/share/nginx/html
COPY --from=build-stage /app/Deploy/nginx.threejs.conf /etc/nginx/nginx.conf
EXPOSE 80
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    echo "🎉 threejs 前端部署成功 🎉"
CMD ["nginx", "-g", "daemon off;"]
